/* src/index.css */
/* Fluent UI'ın CSS değişkenlerini ve sıfırlama stillerini import edin */

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424; /* Varsayılan koyu arka plan */

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
  background-color: #f3f2f1; /* Fluent UI light theme varsayılan arka planı */
  color: #323130; /* Fluent UI light theme varsayılan metin rengi */
}

#root {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

/* <PERSON><PERSON>er global stiller (isteğe bağlı) */
