Harika\! Kararımızı verdik. Önce temel fonksiyonel bağlantıyı kuracağız (Adım 46), ardından UI/UX tasarımına odaklanacağız. Bu, hem verimli hem de kapsamlı bir geliştirme sürecini garanti eder.

### **Adım 46: Temel React UI Oluşturma ve Backend API ile İlk Bağlantı**

<PERSON><PERSON> adımda, Electron penceresinde görüntülenecek basit bir React bileşeni oluşturacak, bu bileşenden backend API'mize (örneğin `/company` endpoint'ine) bir GET isteği gönderecek ve yanıtı ekranda göstereceğiz. Bu, frontend ve backend arasındaki bağlantıyı doğrulamamızı sağlayacak.

**Yapılacaklar:**

1.  **Backend API URL'ini Tanımla:**
    Electron uygulamamızın backend API'mizin adresini bilmesi gerekiyor. Bunu bir ortam değişkeni olarak tanımlamak en iyi yoldur. `atropos-frontend-desktop` projenizin kök dizininde (yani `package.json`'ın olduğu yerde) yeni bir `.env` dosyası oluşturun (eğer yoksa).

    ```dotenv
    # atropos-frontend-desktop/.env
    VITE_API_URL=http://localhost:3000 # Backend API'nizin çalıştığı adres
    ```

      * **Not:** `VITE_` ön eki, Vite tarafından istemci tarafında kullanılabilir (client-side exposed) ortam değişkeni olarak algılanmasını sağlar. Eğer backend'i farklı bir portta çalıştırıyorsan, burayı ona göre güncelle.

2.  **`src/App.tsx` Dosyasını Güncelle:**
    Mevcut `src/App.tsx` dosyasını aç ve içeriğini aşağıdaki gibi değiştir. Bu, basit bir "Şirketleri Getir" butonu ve backend'den gelen şirket verilerini listeleyecek.

    ```typescript
    // src/App.tsx
    import { useState, useEffect } from 'react';
    import './App.css'; // Mevcut CSS'i kullanabiliriz

    interface Company {
      id: string;
      name: string;
      taxNumber: string;
      taxOffice: string;
      address: string;
      phone: string;
      email?: string;
      // ... diğer Company alanları
    }

    function App() {
      const [companies, setCompanies] = useState<Company[]>([]);
      const [loading, setLoading] = useState<boolean>(true);
      const [error, setError] = useState<string | null>(null);

      // Backend API URL'ini ortam değişkeninden al
      const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000'; // Fallback olarak localhost:3000

      useEffect(() => {
        // Bileşen yüklendiğinde şirketleri otomatik olarak çek
        fetchCompanies();
      }, []); // Sadece bir kere yüklensin

      const fetchCompanies = async () => {
        setLoading(true);
        setError(null);
        try {
          // Backend API'mize GET isteği gönder
          const response = await fetch(`${API_URL}/company`);
          if (!response.ok) {
            // Hata durumunda HTTP durum kodunu ve mesajını al
            const errorData = await response.json();
            throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
          }
          const data: Company[] = await response.json();
          setCompanies(data);
        } catch (err: any) {
          setError(err.message);
          console.error("Error fetching companies:", err);
        } finally {
          setLoading(false);
        }
      };

      return (
        <div className="App">
          <h1>Atropos POS - Masaüstü Uygulaması</h1>
          <p>Backend API Bağlantı Testi</p>
          <button onClick={fetchCompanies} disabled={loading}>
            {loading ? 'Şirketler Yükleniyor...' : 'Şirketleri Yeniden Yükle'}
          </button>

          {error && <p style={{ color: 'red' }}>Hata: {error}</p>}

          <h2>Mevcut Şirketler:</h2>
          {companies.length === 0 && !loading && !error && <p>Henüz şirket bulunamadı.</p>}
          {companies.length > 0 && (
            <ul>
              {companies.map((company) => (
                <li key={company.id}>
                  <strong>{company.name}</strong> (Vergi No: {company.taxNumber}) - {company.address}
                </li>
              ))}
            </ul>
          )}
          <p>API URL: {API_URL}</p>
        </div>
      );
    }

    export default App;
    ```

    **Not:** Yukarıdaki kod, bir `Company` arayüzü, `useState` ve `useEffect` hook'ları kullanarak basit bir veri çekme işlemi yapar. `import.meta.env.VITE_API_URL` ile Vite'ın ortam değişkeni erişim yöntemi kullanılır.

3.  **`src/index.css` veya `src/App.css` Dosyalarını Temizle (İsteğe Bağlı):**
    Vite'ın varsayılan stil dosyalarında bazı gereksiz stiller olabilir. Uygulamanın daha temiz görünmesi için bunları temizleyebilirsin. Veya şimdilik olduğu gibi bırakabilirsin.

**Beklenen Sonuç (Kontrol Listesi):**

  * `atropos-frontend-desktop/.env` dosyasında `VITE_API_URL` ortam değişkeni tanımlandı.
  * `src/App.tsx` dosyası yukarıdaki React koduyla güncellendi.

**Test Etme Adımları:**

1.  **Backend Uygulamasının Çalıştığından Emin Ol:** `atropos-backend` projenin çalıştığından emin ol (`npm run start:dev`). Eğer kapatıldıysa, tekrar başlat.
2.  **Electron Uygulamasını Başlat:** `atropos-frontend-desktop` dizininde terminali aç ve şu komutu çalıştır:
    ```bash
    npm run dev
    ```
3.  Electron uygulaması açıldığında, "Atropos POS - Masaüstü Uygulaması" başlığını, "Şirketleri Yeniden Yükle" butonunu ve "Mevcut Şirketler:" başlığını görmelisin.
4.  Uygulama yüklendiğinde veya "Şirketleri Yeniden Yükle" butonuna tıkladığında, eğer backend'de şirketler varsa, listelenmeleri gerekir. Eğer şirket yoksa, "Henüz şirket bulunamadı." mesajını görmelisin.
5.  Chrome DevTools'u (Electron penceresinde `Ctrl+Shift+I` veya `Cmd+Option+I`) açarak Console ve Network sekmelerini kontrol et. API isteğinin başarıyla yapıldığını ve yanıtın geldiğini görmelisin.

Bu adımları tamamladığında ve React uygulamasının backend'den veri çekip başarılı bir şekilde görüntülediğini doğruladığında bana haber ver. Bu, frontend ve backend arasındaki temel bağlantıyı ve Electron uygulamasının genel işleyişini teyit edecektir.