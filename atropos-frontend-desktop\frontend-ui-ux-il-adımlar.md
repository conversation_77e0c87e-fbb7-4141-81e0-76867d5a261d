Harika\! Vizyonumuzu netleştirdiğimize ve UI framework'ümüzü (`Fluent UI React`) seçtiğimize göre, şimdi Atropos POS masaüstü uygulamasının frontend geliştirmesine başlayabiliriz. İlk olarak, kullanıcıların uygulamaya erişim noktası olan Giriş Ekranını oluşturacağız ve genel uygulama iskeletini (layout) kuracağız.

### **Adım 48: Fluent UI React ile Giriş Ekranı ve Temel Uygulama Layout'u Oluşturma**

Bu adımda, Windows 11 estetiğini yansıtan bir giriş ekranı tasarlayacak ve temel React bileşenlerini kullanarak backend `AuthModule`'ümüz ile entegre edeceğiz. Ayrıca, uygulamanın genel layout yapısını da belirleyeceğiz.

**Yapılacaklar:**

1.  **Fluent UI React Bağımlılıklarını Yükle:**
    `atropos-frontend-desktop` projenizin kök dizininde terminali açın ve Fluent UI'ın temel bağımlılıklarını yükleyin:

    ```bash
    npm install @fluentui/react-components @fluentui/react-icons
    ```

      * `@fluentui/react-components`: Fluent UI'ın temel bileşenleri.
      * `@fluentui/react-icons`: Fluent UI ikonları.

2.  **`src/index.css` Dosyasını Güncelle (Fluent UI Temel Stilleri):**
    `src/index.css` dosyasını açın ve Fluent UI'ın temel tema ve sıfırlama stillerini ekleyin. Bu, bileşenlerin doğru görünmesini sağlar.

    ```css
    /* src/index.css */
    /* Fluent UI'ın CSS değişkenlerini ve sıfırlama stillerini import edin */
    @import "@fluentui/react-components/dist/unstable/react-components.css";

    :root {
      font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
      line-height: 1.5;
      font-weight: 400;

      color-scheme: light dark;
      color: rgba(255, 255, 255, 0.87);
      background-color: #242424; /* Varsayılan koyu arka plan */

      font-synthesis: none;
      text-rendering: optimizeLegibility;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    body {
      margin: 0;
      display: flex;
      place-items: center;
      min-width: 320px;
      min-height: 100vh;
      background-color: #f3f2f1; /* Fluent UI light theme varsayılan arka planı */
      color: #323130; /* Fluent UI light theme varsayılan metin rengi */
    }

    #root {
      width: 100%;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }

    /* Diğer global stiller (isteğe bağlı) */
    ```

3.  **`src/auth/LoginScreen.tsx` Bileşenini Oluştur:**
    `src` klasörünün altına `auth` adında yeni bir klasör oluşturun ve içine `LoginScreen.tsx` adında giriş ekranı bileşenini ekleyin. Bu, Windows 11 giriş ekranı estetiğinden ilham alacak.

    ```bash
    mkdir src/auth
    ```

    ```typescript
    // src/auth/LoginScreen.tsx
    import React, { useState } from 'react';
    import {
      Button,
      Field,
      Input,
      makeStyles,
      shorthands,
      tokens,
      Text,
      Caption,
      Spinner,
      Card,
      CardHeader,
      Avatar,
    } from '@fluentui/react-components';
    import { CheckmarkCircleFilled, DismissCircleFilled } from '@fluentui/react-icons'; // İkonlar için

    // Stil tanımlamaları (Windows 11 login ekranından esinlenerek)
    const useStyles = makeStyles({
      container: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        ...shorthands.padding('24px'),
        backgroundColor: tokens.colorNeutralBackground1, // Windows 11 blur efekti yerine düz renk, ama Fluent UI temasına uygun
        ...shorthands.borderRadius(tokens.borderRadiusXLarge),
        boxShadow: tokens.shadow64, // Biraz derinlik kat
      },
      loginBox: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        ...shorthands.gap('20px'),
        minWidth: '350px',
        maxWidth: '400px',
      },
      avatarContainer: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        ...shorthands.gap('8px'),
      },
      form: {
        display: 'flex',
        flexDirection: 'column',
        ...shorthands.gap('16px'),
        width: '100%',
      },
      message: {
        textAlign: 'center',
        ...shorthands.padding('8px'),
        ...shorthands.borderRadius('4px'),
      },
      success: {
        backgroundColor: tokens.colorPaletteGreenBackground3,
        color: tokens.colorPaletteGreenForeground3,
      },
      error: {
        backgroundColor: tokens.colorPaletteRedBackground3,
        color: tokens.colorPaletteRedForeground3,
      },
      // Windows 11 login ekranındaki 'Sign-in options' ve 'Other user' benzeri alanlar için
      options: {
        display: 'flex',
        ...shorthands.gap('12px'),
        marginTop: '10px',
      },
      otherUser: {
        marginTop: '20px',
        ...shorthands.padding('10px 0'),
        borderTop: `1px solid ${tokens.colorNeutralStroke1}`,
        width: '100%',
        textAlign: 'center',
      },
    });

    interface LoginScreenProps {
      onLoginSuccess: (token: string) => void;
    }

    const LoginScreen: React.FC<LoginScreenProps> = ({ onLoginSuccess }) => {
      const styles = useStyles();
      const [username, setUsername] = useState('');
      const [password, setPassword] = useState('');
      const [loading, setLoading] = useState(false);
      const [error, setError] = useState<string | null>(null);
      const [successMessage, setSuccessMessage] = useState<string | null>(null);

      const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';

      const handleLogin = async (event: React.FormEvent) => {
        event.preventDefault(); // Formun varsayılan gönderimini engelle
        setLoading(true);
        setError(null);
        setSuccessMessage(null);

        try {
          const response = await fetch(`${API_URL}/auth/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username, password }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Giriş başarısız oldu.');
          }

          const data = await response.json();
          setSuccessMessage('Giriş başarılı! Yönlendiriliyorsunuz...');
          // Token'ı bir sonraki adımda güvenli bir şekilde saklayacağız
          onLoginSuccess(data.access_token);
        } catch (err: any) {
          setError(err.message);
          console.error('Login error:', err);
        } finally {
          setLoading(false);
        }
      };

      return (
        <div className={styles.container}>
          <div className={styles.loginBox}>
            <div className={styles.avatarContainer}>
              <Avatar name={username || "Kullanıcı"} size={128} /> {/* Varsayılan avatar */}
              <Text as="h2" size={800}>{username || "Kullanıcı Adı"}</Text>
              <Caption>Lütfen şifrenizi girin.</Caption>
            </div>

            <form onSubmit={handleLogin} className={styles.form}>
              <Field label="Kullanıcı Adı" required>
                <Input
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  placeholder="Kullanıcı adınız"
                  disabled={loading}
                />
              </Field>
              <Field label="Şifre" required>
                <Input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Şifreniz"
                  disabled={loading}
                />
              </Field>

              {error && (
                <Card className={`${styles.message} ${styles.error}`} appearance="outline">
                  <CardHeader
                    header={<Text weight="semibold">Hata:</Text>}
                    description={<Caption>{error}</Caption>}
                    action={<DismissCircleFilled primaryFill={tokens.colorPaletteRedForeground3} />}
                  />
                </Card>
              )}
              {successMessage && (
                <Card className={`${styles.message} ${styles.success}`} appearance="outline">
                  <CardHeader
                    header={<Text weight="semibold">Başarılı:</Text>}
                    description={<Caption>{successMessage}</Caption>}
                    action={<CheckmarkCircleFilled primaryFill={tokens.colorPaletteGreenForeground3} />}
                  />
                </Card>
              )}

              <Button type="submit" appearance="primary" disabled={loading}>
                {loading ? <Spinner size="tiny" labelPosition="after" label="Giriş Yapılıyor..." /> : 'Giriş Yap'}
              </Button>
            </form>

            {/* Windows 11 Login ekranındaki diğer seçenekler benzeri butonlar */}
            <div className={styles.options}>
              <Button appearance="subtle">Giriş Seçenekleri</Button>
              <Button appearance="subtle">PIN ile Giriş</Button>
            </div>

            <Text className={styles.otherUser} size={300} weight="semibold">
              <Button appearance="subtle">Başka bir kullanıcı</Button>
            </Text>
          </div>
        </div>
      );
    };

    export default LoginScreen;
    ```

    **Not:** Bu bileşen, sadece bir giriş formu sunar. Başarılı girişte `onLoginSuccess` prop'u çağrılır. `Avatar` için kullanıcının gerçek avatar URL'si veya `username`'den türetilen bir ad kullanılabilir.

4.  **`src/dashboard/MainDashboard.tsx` Bileşenini Oluştur (Uygulamanın Ana Layout'u):**
    `src` klasörünün altına `dashboard` adında yeni bir klasör oluşturun ve içine `MainDashboard.tsx` adında temel uygulama layout'unu ekleyin. Bu, başarılı giriş sonrası görüntülenecek ana içerik alanı olacak.

    ```bash
    mkdir src/dashboard
    ```

    ```typescript
    // src/dashboard/MainDashboard.tsx
    import React from 'react';
    import {
      makeStyles,
      shorthands,
      tokens,
      Text,
      Toolbar,
      ToolbarButton,
      ToolbarDivider,
      TabList,
      Tab,
      Button,
    } from '@fluentui/react-components';
    import {
      HomeFilled,
      ListFilled,
      MoneyFilled,
      SettingsFilled,
      ArrowExitFilled,
      TableFilled,
      FoodFilled,
      PeopleFilled,
      ChartFilled,
      InventoryFilled,
      // Diğer ikonlar için
    } from '@fluentui/react-icons';

    // Stil tanımlamaları
    const useStyles = makeStyles({
      root: {
        display: 'flex',
        flexDirection: 'column',
        width: '100vw',
        height: '100vh',
        backgroundColor: tokens.colorNeutralBackground2, // Windows 11 masaüstü gibi hafif bir renk
      },
      header: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        ...shorthands.padding('10px 20px'),
        backgroundColor: tokens.colorBrandBackground, // Ana arka plan rengi, Windows aksan rengi gibi
        color: tokens.colorNeutralForegroundOnBrand,
        boxShadow: tokens.shadow4,
      },
      headerLeft: {
        display: 'flex',
        alignItems: 'center',
        ...shorthands.gap('10px'),
      },
      headerRight: {
        display: 'flex',
        alignItems: 'center',
        ...shorthands.gap('10px'),
      },
      sidebar: {
        // Sol menü (Windows 11 Başlat Menüsü veya Kenar Çubuğu gibi)
        display: 'flex',
        flexDirection: 'column',
        ...shorthands.padding('10px'),
        backgroundColor: tokens.colorNeutralBackground1,
        boxShadow: tokens.shadow8,
        ...shorthands.borderRight('1px', 'solid', tokens.colorNeutralStroke1),
        minWidth: '80px', // Kompakt
        maxWidth: '200px', // Açık
      },
      content: {
        flexGrow: 1,
        ...shorthands.padding('20px'),
        display: 'flex',
        flexDirection: 'column',
        // Buraya asıl uygulama içeriği gelecek
      },
      footer: {
        display: 'flex',
        justifyContent: 'flex-end', // Sağ altta saat vs. gibi
        ...shorthands.padding('10px 20px'),
        backgroundColor: tokens.colorNeutralBackground1,
        boxShadow: tokens.shadow8,
        color: tokens.colorNeutralForeground2,
      },
      mainLayout: {
        display: 'flex',
        flexGrow: 1,
      },
      toolbar: {
        backgroundColor: tokens.colorNeutralBackground1,
        boxShadow: tokens.shadow8,
        marginBottom: '10px',
        ...shorthands.padding('5px'),
        ...shorthands.gap('5px'),
      },
      tabList: {
        ...shorthands.padding('10px'),
      },
    });

    interface MainDashboardProps {
      onLogout: () => void;
    }

    const MainDashboard: React.FC<MainDashboardProps> = ({ onLogout }) => {
      const styles = useStyles();

      return (
        <div className={styles.root}>
          {/* Header / Başlık Çubuğu */}
          <div className={styles.header}>
            <div className={styles.headerLeft}>
              <Text weight="bold" size={600}>Atropos POS</Text>
              <Text size={300}>v1.0</Text>
            </div>
            <div className={styles.headerRight}>
              <Text>Kullanıcı: Admin</Text> {/* Buraya dinamik kullanıcı adı gelecek */}
              <Button onClick={onLogout} appearance="subtle" icon={<ArrowExitFilled />}>Çıkış Yap</Button>
            </div>
          </div>

          <div className={styles.mainLayout}>
            {/* Sol Menü / Sidebar (Windows Başlat Menüsü Konsepti) */}
            <div className={styles.sidebar}>
              <TabList defaultSelectedValue="home" vertical className={styles.tabList}>
                <Tab value="home" icon={<HomeFilled />}>Ana Sayfa</Tab>
                <Tab value="orders" icon={<ListFilled />}>Siparişler</Tab>
                <Tab value="tables" icon={<TableFilled />}>Masalar</Tab>
                <Tab value="payments" icon={<MoneyFilled />}>Ödemeler</Tab>
                <Tab value="products" icon={<FoodFilled />}>Ürünler</Tab>
                <Tab value="customers" icon={<PeopleFilled />}>Müşteriler</Tab>
                <Tab value="inventory" icon={<InventoryFilled />}>Envanter</Tab>
                <Tab value="reports" icon={<ChartFilled />}>Raporlar</Tab>
                <Tab value="settings" icon={<SettingsFilled />}>Ayarlar</Tab>
              </TabList>
            </div>

            {/* Ana İçerik Alanı */}
            <div className={styles.content}>
              <Toolbar className={styles.toolbar}>
                <ToolbarButton icon={<HomeFilled />}>Dashboard</ToolbarButton>
                <ToolbarDivider />
                <ToolbarButton icon={<ListFilled />}>Yeni Sipariş</ToolbarButton>
                <ToolbarButton icon={<TableFilled />}>Masa Yönetimi</ToolbarButton>
              </Toolbar>
              <Text size={700} weight="bold">Ana Ekran İçeriği Buraya Gelecek</Text>
              <p>Modülleri seçerek ilgili içeriği burada görüntüleyeceğiz.</p>
              {/* Buraya seçilen modülün içeriği gelecek */}
            </div>
          </div>

          {/* Alt Bilgi / Footer (Windows Görev Çubuğu Sağ Köşe Benzeri) */}
          <div className={styles.footer}>
            <Text>Atropos POS v1.0 | 2025</Text>
          </div>
        </div>
      );
    };

    export default MainDashboard;
    ```

    **Not:** Bu bileşen, genel uygulama düzenini (header, sidebar, content, footer) belirler. Soldaki `TabList` menüsü, farklı modüllere geçiş yapmak için işletim sistemi menüsü konseptinden ilham alır. `onLogout` prop'u, çıkış işlemini tetiklemek için kullanılır.

5.  **`src/App.tsx` Dosyasını Güncelle (Giriş Durumunu Yönetmek İçin):**
    `src/App.tsx` dosyasını aç ve giriş ekranı ile ana gösterge paneli (`MainDashboard`) arasında geçiş yapacak mantığı ekleyin. Kullanıcının giriş durumunu (JWT token'ın varlığı) React state'i ile yöneteceğiz.

    ```typescript
    // src/App.tsx
    import React, { useState, useEffect } from 'react';
    import './App.css';
    import LoginScreen from './auth/LoginScreen'; // LoginScreen'i import et
    import MainDashboard from './dashboard/MainDashboard'; // MainDashboard'u import et

    function App() {
      const [token, setToken] = useState<string | null>(null); // JWT token'ı state'i

      // Uygulama yüklendiğinde token'ı localStorage'dan kontrol et
      useEffect(() => {
        const storedToken = localStorage.getItem('atropos_pos_token');
        if (storedToken) {
          setToken(storedToken);
        }
      }, []);

      const handleLoginSuccess = (newToken: string) => {
        setToken(newToken);
        localStorage.setItem('atropos_pos_token', newToken); // Token'ı localStorage'da sakla
        // Başarılı giriş sonrası kullanıcıyı Dashboard'a yönlendir
        // (Şu an için direkt state değişimi ile render edilecek)
      };

      const handleLogout = () => {
        setToken(null);
        localStorage.removeItem('atropos_pos_token'); // Token'ı sil
        // Kullanıcıyı Login ekranına yönlendir
      };

      // Token varsa Dashboard'ı, yoksa LoginScreen'i render et
      return (
        <div className="App-container"> {/* App.css'de bu stilin tanımlı olduğundan emin ol */}
          {token ? (
            <MainDashboard onLogout={handleLogout} />
          ) : (
            <LoginScreen onLoginSuccess={handleLoginSuccess} />
          )}
        </div>
      );
    }

    export default App;
    ```

    **Not:** `localStorage` token saklamak için basit bir yoldur, ancak daha güvenli bir üretim ortamı için **güvenli HTTP-only çerezler** veya **Electron Secure Storage** gibi çözümler düşünülmelidir. Şu anki aşama için basit bir başlangıç sağlıyor.

6.  **`App.css` dosyasında `App-container` stilini tanımla (isteğe bağlı ama önerilir):**
    `src/App.css` dosyanızı açın ve `<div className="App-container">` için uygun stilleri ekleyin.

    ```css
    /* src/App.css */
    .App-container {
      width: 100vw;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f3f2f1; /* Windows 11 masaüstü varsayılan arka planı */
    }

    /* Diğer stil ayarlamaları için */
    /* Fluent UI bileşenleri için bazı genel sıfırlamalar veya font ayarları */
    body {
      font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif; /* Windows 11 fontu gibi */
      margin: 0;
    }
    ```

**Beklenen Sonuç (Kontrol Listesi):**

  * `@fluentui/react-components` ve `@fluentui/react-icons` paketleri `package.json`'a eklendi.
  * `src/index.css` dosyasına Fluent UI temel stilleri eklendi.
  * `src/auth/LoginScreen.tsx` ve `src/dashboard/MainDashboard.tsx` bileşenleri oluşturuldu.
  * `src/App.tsx` dosyası, giriş durumunu yöneten ve `LoginScreen` ile `MainDashboard` arasında geçiş yapan mantıkla güncellendi.
  * `src/App.css` dosyasına `App-container` stili eklendi.

**Test Etme Adımları:**

1.  **Backend Uygulamasının Çalıştığından Emin Ol:** `atropos-backend` projenin çalıştığından emin ol (`npm run start:dev`).
2.  **Electron Uygulamasını Başlat:** `atropos-frontend-desktop` dizininde terminali aç ve şu komutu çalıştır:
    ```bash
    npm run dev
    ```
3.  Electron uygulaması açıldığında:
      * **Giriş Ekranını Görmelisin:** `LoginScreen.tsx` bileşeni (Windows 11 giriş ekranı estetiğinde) karşına gelmeli.
      * **Giriş Yapmayı Dene:** Daha önce oluşturduğun bir Admin kullanıcısının `username` ve `password`'u ile giriş yap.
      * **Başarılı Giriş Sonrası:** `MainDashboard.tsx` bileşeninin (header, sidebar, content, footer) görüntülendiğini görmelisin.
      * **Çıkış Yapmayı Dene:** Dashboard'daki "Çıkış Yap" butonuna tıkla. Tekrar `LoginScreen`'e yönlendirilmelisin.
      * **Yanlış Kimlik Bilgileri:** Yanlış kullanıcı adı/şifre ile giriş yapmaya çalış. Hata mesajını görmelisin.
      * **DevTools Kontrolü:** Chrome DevTools'u açarak (Ctrl+Shift+I) Network sekmesinde `auth/login` isteğinin başarıyla yapıldığını ve token aldığını, ardından `profile` isteği gibi diğer dashboard bileşenlerinin backend'den veri çekmeye çalışacağını (henüz çekmese bile) görebilirsin.

Bu adımları tamamladığında ve giriş/çıkış akışının beklendiği gibi çalıştığını doğruladığında bana haber ver. Bu, frontend geliştirme sürecimizin ilk büyük adımıdır ve UI/UX vizyonumuzun hayata geçtiğini gösterecektir.