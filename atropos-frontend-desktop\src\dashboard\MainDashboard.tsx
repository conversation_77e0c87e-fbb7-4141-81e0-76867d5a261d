// src/dashboard/MainDashboard.tsx
import React from 'react';
import {
  makeStyles,
  shorthands,
  tokens,
  Text,
  Toolbar,
  ToolbarButton,
  ToolbarDivider,
  TabList,
  <PERSON>b,
  <PERSON><PERSON>,
} from '@fluentui/react-components';
import {
  HomeFilled,
  ListFilled,
  MoneyFilled,
  SettingsFilled,
  ArrowExitFilled,
  GridFilled,
  FoodFilled,
  PeopleFilled,
  DataBarVerticalFilled,
  BoxFilled,
  // Diğer ikonlar için
} from '@fluentui/react-icons';

// Stil tanımlamaları
const useStyles = makeStyles({
  root: {
    display: 'flex',
    flexDirection: 'column',
    width: '100vw',
    height: '100vh',
    backgroundColor: tokens.colorNeutralBackground2, // Windows 11 masaüstü gibi hafif bir renk
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    ...shorthands.padding('10px 20px'),
    backgroundColor: tokens.colorBrandBackground, // Ana arka plan rengi, Windows aksan rengi gibi
    color: tokens.colorNeutralForegroundOnBrand,
    boxShadow: tokens.shadow4,
  },
  headerLeft: {
    display: 'flex',
    alignItems: 'center',
    ...shorthands.gap('10px'),
  },
  headerRight: {
    display: 'flex',
    alignItems: 'center',
    ...shorthands.gap('10px'),
  },
  sidebar: {
    // Sol menü (Windows 11 Başlat Menüsü veya Kenar Çubuğu gibi)
    display: 'flex',
    flexDirection: 'column',
    ...shorthands.padding('10px'),
    backgroundColor: tokens.colorNeutralBackground1,
    boxShadow: tokens.shadow8,
    ...shorthands.borderRight('1px', 'solid', tokens.colorNeutralStroke1),
    minWidth: '80px', // Kompakt
    maxWidth: '200px', // Açık
  },
  content: {
    flexGrow: 1,
    ...shorthands.padding('20px'),
    display: 'flex',
    flexDirection: 'column',
    // Buraya asıl uygulama içeriği gelecek
  },
  footer: {
    display: 'flex',
    justifyContent: 'flex-end', // Sağ altta saat vs. gibi
    ...shorthands.padding('10px 20px'),
    backgroundColor: tokens.colorNeutralBackground1,
    boxShadow: tokens.shadow8,
    color: tokens.colorNeutralForeground2,
  },
  mainLayout: {
    display: 'flex',
    flexGrow: 1,
  },
  toolbar: {
    backgroundColor: tokens.colorNeutralBackground1,
    boxShadow: tokens.shadow8,
    marginBottom: '10px',
    ...shorthands.padding('5px'),
    ...shorthands.gap('5px'),
  },
  tabList: {
    ...shorthands.padding('10px'),
  },
});

interface MainDashboardProps {
  onLogout: () => void;
}

const MainDashboard: React.FC<MainDashboardProps> = ({ onLogout }) => {
  const styles = useStyles();

  return (
    <div className={styles.root}>
      {/* Header / Başlık Çubuğu */}
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <Text weight="bold" size={600}>Atropos POS</Text>
          <Text size={300}>v1.0</Text>
        </div>
        <div className={styles.headerRight}>
          <Text>Kullanıcı: Admin</Text> {/* Buraya dinamik kullanıcı adı gelecek */}
          <Button onClick={onLogout} appearance="subtle" icon={<ArrowExitFilled />}>Çıkış Yap</Button>
        </div>
      </div>

      <div className={styles.mainLayout}>
        {/* Sol Menü / Sidebar (Windows Başlat Menüsü Konsepti) */}
        <div className={styles.sidebar}>
          <TabList defaultSelectedValue="home" vertical className={styles.tabList}>
            <Tab value="home" icon={<HomeFilled />}>Ana Sayfa</Tab>
            <Tab value="orders" icon={<ListFilled />}>Siparişler</Tab>
            <Tab value="tables" icon={<GridFilled />}>Masalar</Tab>
            <Tab value="payments" icon={<MoneyFilled />}>Ödemeler</Tab>
            <Tab value="products" icon={<FoodFilled />}>Ürünler</Tab>
            <Tab value="customers" icon={<PeopleFilled />}>Müşteriler</Tab>
            <Tab value="inventory" icon={<BoxFilled />}>Envanter</Tab>
            <Tab value="reports" icon={<DataBarVerticalFilled />}>Raporlar</Tab>
            <Tab value="settings" icon={<SettingsFilled />}>Ayarlar</Tab>
          </TabList>
        </div>

        {/* Ana İçerik Alanı */}
        <div className={styles.content}>
          <Toolbar className={styles.toolbar}>
            <ToolbarButton icon={<HomeFilled />}>Dashboard</ToolbarButton>
            <ToolbarDivider />
            <ToolbarButton icon={<ListFilled />}>Yeni Sipariş</ToolbarButton>
            <ToolbarButton icon={<GridFilled />}>Masa Yönetimi</ToolbarButton>
          </Toolbar>
          <Text size={700} weight="bold">Ana Ekran İçeriği Buraya Gelecek</Text>
          <p>Modülleri seçerek ilgili içeriği burada görüntüleyeceğiz.</p>
          {/* Buraya seçilen modülün içeriği gelecek */}
        </div>
      </div>

      {/* Alt Bilgi / Footer (Windows Görev Çubuğu Sağ Köşe Benzeri) */}
      <div className={styles.footer}>
        <Text>Atropos POS v1.0 | 2025</Text>
      </div>
    </div>
  );
};

export default MainDashboard;
